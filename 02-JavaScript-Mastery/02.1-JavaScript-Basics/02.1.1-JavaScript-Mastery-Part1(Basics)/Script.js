/*
=============================================================================
                        JAVASCRIPT MASTERY PART 1: BASICS
=============================================================================
This file contains comprehensive examples of JavaScript fundamentals including:
- Arrays and Objects
- Functions, Scope & Execution Context
- Logic & Control Flow
- Array Methods (forEach, filter, map, reduce)

Each section is thoroughly documented with explanations and examples.
To run any section, uncomment the code block and refresh the page.
=============================================================================
*/

// *********** SECTION 1: ARRAYS & OBJECTS ***********

/*
=============================================================================
                            ARRAY BASICS
=============================================================================
Arrays are ordered collections of elements that can store multiple values.
JavaScript arrays are dynamic and can hold different data types.
=============================================================================
*/

/*
// ARRAY CREATION AND ACCESS EXAMPLES
let x; // Variable to store our results for demonstration

// METHOD 1: Array Literal Syntax (Most Common)
const numbers = [12, 45, 33, 29, 39, 10, 13];
/*
Array Literal Features:
- Uses square brackets []
- Elements separated by commas
- Most common and preferred method
- Can contain mixed data types
*/

// METHOD 2: Array Constructor Syntax
const fruit = new Array("Apple", "Banana", "Orange", "Pear");
/*
Array Constructor Features:
- Uses 'new Array()' syntax
- Less commonly used
- Can be confusing with single numeric argument
- Example: new Array(5) creates empty array with 5 slots
*/

// ARRAY ACCESS EXAMPLES
x = numbers[0];                    // Access first element (index 0) → 12
x = numbers[0] + numbers[3];       // Add first and fourth elements → 12 + 29 = 41
x = `My favorite fruit is ${fruit[3]}`; // Template literal with array access → "My favorite fruit is Pear"
x = numbers.length;                // Get array length → 7

/*
Array Indexing Rules:
- Arrays are zero-indexed (first element is at index 0)
- Last element is at index (length - 1)
- Accessing non-existent index returns undefined
- Negative indices are not supported (unlike Python)
*/

console.log(x); // Output the final value of x
*/

/*
=============================================================================
                        ARRAY METHODS & MANIPULATION
=============================================================================
JavaScript provides many built-in methods to manipulate arrays.
These methods either modify the original array (mutating) or return new arrays.
=============================================================================
*/

/*
// ARRAY METHODS DEMONSTRATION
let x;
const arr = [1, 2, 3, 4, 5]; // Starting array

// ADDING ELEMENTS
arr.push(6);        // Add element to the END → [1, 2, 3, 4, 5, 6]
/*
push() method:
- Adds one or more elements to the end of array
- Returns the new length of the array
- Mutates the original array
- Example: arr.push(7, 8, 9) adds multiple elements
*/

arr.unshift(0);     // Add element to the BEGINNING → [0, 1, 2, 3, 4, 5, 6]
/*
unshift() method:
- Adds one or more elements to the beginning of array
- Returns the new length of the array
- Mutates the original array
- More expensive than push() due to index shifting
*/

// REMOVING ELEMENTS
arr.pop();          // Remove element from the END → [0, 1, 2, 3, 4, 5]
/*
pop() method:
- Removes and returns the last element
- Returns undefined if array is empty
- Mutates the original array
- Opposite of push()
*/

arr.shift();        // Remove element from the BEGINNING → [1, 2, 3, 4, 5]
/*
shift() method:
- Removes and returns the first element
- Returns undefined if array is empty
- Mutates the original array
- More expensive than pop() due to index shifting
*/

arr.splice(2, 1);   // Remove 1 element at index 2 → [1, 2, 4, 5]
/*
splice() method:
- splice(startIndex, deleteCount, ...itemsToAdd)
- Most versatile array method
- Can add, remove, or replace elements
- Returns array of removed elements
- Examples:
  - arr.splice(2, 0, 'new') // Insert 'new' at index 2
  - arr.splice(1, 2, 'a', 'b') // Replace 2 elements with 'a', 'b'
*/

// ARRAY TRANSFORMATION
arr.reverse();      // Reverse array order → [5, 4, 2, 1]
/*
reverse() method:
- Reverses array elements in place
- Mutates the original array
- Returns reference to the same array
*/

arr.sort();         // Sort as strings → [1, 2, 4, 5]
/*
sort() method (default):
- Sorts elements as strings by default
- Can produce unexpected results with numbers
- Example: [10, 2, 1].sort() → [1, 10, 2]
- Mutates the original array
*/

arr.sort((a, b) => a - b); // Sort numerically ascending → [1, 2, 4, 5]
/*
sort() with compare function:
- (a, b) => a - b: ascending order
- (a, b) => b - a: descending order
- Compare function returns:
  - Negative: a comes before b
  - Zero: a and b are equal
  - Positive: a comes after b
*/

// SEARCH METHODS
x = arr.includes(4);    // Check if array contains value → true
/*
includes() method:
- Returns boolean (true/false)
- Uses strict equality (===)
- More readable than indexOf() for existence checks
- Example: arr.includes(4) → true
*/

x = arr.indexOf(4);     // Find index of value → 2
/*
indexOf() method:
- Returns first index where element is found
- Returns -1 if element not found
- Uses strict equality (===)
- Example: arr.indexOf(99) → -1
*/

console.log(arr);   // Show final array state
console.log(x);     // Show final value of x
*/

/*
=============================================================================
                    ARRAY COMBINATION & ADVANCED OPERATIONS
=============================================================================
Advanced array operations including combining arrays, flattening nested arrays,
and using static Array methods for array creation and validation.
=============================================================================
*/

/*
// ARRAY COMBINATION EXAMPLES
let x;
const fruit = ["apple", "banana", "orange", "pear"];
const berries = ["strawberry", "blueberry", "raspberry"];

// METHOD 1: Nested Arrays (Creating 2D Array)
// fruit.push(berries);  // This would add berries as a single element
// x = fruit[4][1];      // Access nested array element

const allFruits = [fruit, berries]; // Create array of arrays
x = allFruits[0][1];                 // Access "banana" → allFruits[0][1]
/*
Nested Array Structure:
allFruits = [
  ["apple", "banana", "orange", "pear"],           // index 0
  ["strawberry", "blueberry", "raspberry"]         // index 1
]
- allFruits[0] = fruit array
- allFruits[0][1] = "banana"
- allFruits[1][2] = "raspberry"
*/

// METHOD 2: concat() - Combines arrays into new array
x = fruit.concat(berries);
/*
concat() method:
- Creates NEW array (doesn't modify originals)
- Combines multiple arrays
- Returns: ["apple", "banana", "orange", "pear", "strawberry", "blueberry", "raspberry"]
- Can accept multiple arguments: arr1.concat(arr2, arr3, arr4)
- More traditional approach
*/

// METHOD 3: Spread Operator (...) - Modern ES6 approach
x = [...fruit, ...berries];
/*
Spread Operator (...):
- Modern ES6 syntax
- Creates NEW array
- More flexible than concat()
- Can insert elements anywhere: ["start", ...fruit, "middle", ...berries, "end"]
- Can spread any iterable (arrays, strings, etc.)
- Preferred modern approach
*/

// ARRAY FLATTENING
const arr = [1, 2, [3, 4], 5, [6, 7], 8]; // Nested array
x = arr.flat();                            // Flatten one level → [1, 2, 3, 4, 5, 6, 7, 8]
/*
flat() method:
- Flattens nested arrays by specified depth
- Default depth is 1
- Examples:
  - arr.flat()    // Flatten 1 level
  - arr.flat(2)   // Flatten 2 levels
  - arr.flat(Infinity) // Flatten all levels
- Returns new array (doesn't modify original)
*/

// STATIC ARRAY METHODS
x = Array.isArray(fruit);    // Check if variable is an array → true
/*
Array.isArray():
- Static method (called on Array constructor)
- Returns boolean (true/false)
- Most reliable way to check if something is an array
- Better than typeof (which returns "object" for arrays)
- Example: Array.isArray([1,2,3]) → true, Array.isArray("hello") → false
*/

x = Array.from("12345");     // Create array from string → ["1", "2", "3", "4", "5"]
/*
Array.from():
- Creates array from array-like or iterable objects
- Common uses:
  - Array.from("hello") → ["h", "e", "l", "l", "o"]
  - Array.from({length: 3}) → [undefined, undefined, undefined]
  - Array.from({length: 3}, (_, i) => i) → [0, 1, 2]
- Second parameter is optional mapping function
*/

const a = 1;
const b = 2;
const c = 3;
x = Array.of(a, b, c);       // Create array from arguments → [1, 2, 3]
/*
Array.of():
- Creates array from any number of arguments
- Difference from Array constructor:
  - Array.of(3) → [3]
  - new Array(3) → [empty × 3]
- Useful when you want to ensure array creation regardless of argument count
- Example: Array.of(1, 2, 3, 4, 5) → [1, 2, 3, 4, 5]
*/

console.log(x); // Output final result
*/

/*
=============================================================================
                            ARRAY CHALLENGE
=============================================================================
Practical exercises combining multiple array methods to solve real problems.
These challenges demonstrate how array methods work together.
=============================================================================
*/

/*
// CHALLENGE 1: Array Reversal
const arr = [1, 2, 3, 4, 5, 6];
arr.reverse();                    // Reverse in place → [6, 5, 4, 3, 2, 1]
console.log(arr);
/*
Challenge 1 Explanation:
- Original array: [1, 2, 3, 4, 5, 6]
- reverse() mutates the original array
- Result: [6, 5, 4, 3, 2, 1]
- Note: reverse() returns reference to same array
*/

// CHALLENGE 2: Remove and Combine Arrays
const arr1 = [1, 2, 3, 4, 5];
const arr2 = [5, 6, 7, 8, 9, 10];
arr1.pop();                       // Remove last element (5) from arr1 → [1, 2, 3, 4]
x = arr1.concat(arr2);           // Combine arrays → [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
console.log(x);
/*
Challenge 2 Step-by-step:
1. arr1 starts as [1, 2, 3, 4, 5]
2. arr1.pop() removes 5, arr1 becomes [1, 2, 3, 4]
3. concat() combines [1, 2, 3, 4] + [5, 6, 7, 8, 9, 10]
4. Final result: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
5. Note: concat() creates new array, doesn't modify originals
*/
*/

/*
=============================================================================
                            JAVASCRIPT OBJECTS
=============================================================================
Objects are collections of key-value pairs that represent real-world entities.
They are fundamental data structures in JavaScript for organizing related data.
=============================================================================
*/

/*
// OBJECT CREATION AND STRUCTURE
const person = {
    // PRIMITIVE PROPERTIES
    name: 'John',              // String property
    age: 30,                   // Number property
    isStudent: false,          // Boolean property

    // NESTED OBJECT PROPERTY
    address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY'
    },

    // ARRAY PROPERTY
    hobbies: ['reading', 'coding', 'gaming']
};

/*
Object Structure Explanation:
- Objects use curly braces {}
- Properties are key-value pairs separated by commas
- Keys can be strings or identifiers (without quotes if valid identifier)
- Values can be any data type: primitives, objects, arrays, functions
- Nested objects create hierarchical data structures
- Arrays within objects store multiple related values
*/

// ACCESSING OBJECT PROPERTIES
console.log(person.hobbies[0]);    // Access array element within object → "reading"

/*
Property Access Methods:
1. Dot notation: person.name
   - Most common and readable
   - Requires valid identifier names
   - Cannot use variables as property names

2. Bracket notation: person['name'] or person[variable]
   - More flexible
   - Can use variables: person[propertyName]
   - Required for property names with spaces or special characters
   - Can use computed property names

Examples:
- person.name → "John"
- person.address.city → "New York"
- person.hobbies[0] → "reading"
- person['age'] → 30
*/
*/

/*
=============================================================================
                    OBJECT METHODS & ADVANCED OPERATIONS
=============================================================================
Advanced object operations including dynamic property assignment,
object spread operator, and built-in Object methods for manipulation.
=============================================================================
*/

/*
// DYNAMIC OBJECT CREATION
let x;
const todo = {};                    // Create empty object
todo.id = 1;                       // Add properties dynamically
todo.name = "Buy Milk";
todo.completed = false;

/*
Dynamic Property Assignment:
- Start with empty object {}
- Add properties using dot notation
- Properties can be added/modified at runtime
- Flexible but less performant than object literals
*/

x = todo;                          // Assign object reference
/*
Object Assignment:
- Objects are reference types
- x and todo point to the same object
- Modifying x will also modify todo
- Use spread operator or Object.assign() for copying
*/

// NESTED OBJECT ACCESS
const person = {
    address: {
        coords: {
            lat: 42.9384,
            lng: -71.3232
        }
    }
};
x = person.address.coords.lat;     // Deep property access → 42.9384

/*
Nested Object Access:
- Chain dot notation for deep access
- Each level must exist to avoid errors
- Use optional chaining (?.) for safe access: person.address?.coords?.lat
- Consider destructuring for cleaner code
*/

// OBJECT SPREAD OPERATOR
const obj1 = {a: 1, b: 2};
const obj2 = {c: 3, d: 4};
const obj3 = {...obj1, ...obj2};   // Combine objects → {a: 1, b: 2, c: 3, d: 4}

/*
Object Spread Operator:
- Creates new object (shallow copy)
- Combines multiple objects
- Later properties override earlier ones
- Examples:
  - {...obj1, ...obj2} → merge objects
  - {...obj1, b: 99} → override specific property
  - {x: 0, ...obj1} → add property before spreading
*/

// ARRAY OF OBJECTS
const todos = [
    {id: 1, name: 'Buy Milk'},
    {id: 2, name: 'Buy Bread'},
    {id: 3, name: 'Buy Eggs'}
];

/*
Array of Objects Pattern:
- Common data structure for collections
- Each array element is an object
- Useful for representing lists of similar items
- Often used with array methods (map, filter, reduce)
*/

x = todos[0].name;                 // Access object property in array → "Buy Milk"

// BUILT-IN OBJECT METHODS
x = Object.keys(todo).length;      // Get number of properties → 3
/*
Object.keys():
- Returns array of object's property names
- Useful for iteration and property counting
- Example: Object.keys({a: 1, b: 2}) → ["a", "b"]
*/

x = Object.values(todo);           // Get array of property values → [1, "Buy Milk", false]
/*
Object.values():
- Returns array of object's property values
- Useful for processing all values
- Example: Object.values({a: 1, b: 2}) → [1, 2]
*/

x = Object.entries(todo);          // Get array of [key, value] pairs
/*
Object.entries():
- Returns array of [key, value] arrays
- Useful for iteration with both keys and values
- Example: Object.entries({a: 1, b: 2}) → [["a", 1], ["b", 2]]
- Can be used with for...of loops or destructuring
*/

x = todo.hasOwnProperty('age');    // Check if property exists → false
/*
hasOwnProperty():
- Checks if object has specific property (not inherited)
- Returns boolean
- More reliable than checking if property is undefined
- Modern alternative: Object.hasOwn(todo, 'age')
*/

console.log(x); // Output final result
*/

/*
=============================================================================
                    DESTRUCTURING & OBJECT SHORTHAND
=============================================================================
Modern ES6 features for cleaner object creation and property extraction.
These features reduce code verbosity and improve readability.
=============================================================================
*/

/*
// OBJECT PROPERTY SHORTHAND (ES6)
const firstName = 'John';
const lastName = 'Doe';
const age = 30;

// Traditional object creation
// const person = {
//     firstName: firstName,
//     lastName: lastName,
//     age: age
// };

// ES6 Shorthand - when property name matches variable name
const person = {
    firstName,    // Equivalent to firstName: firstName
    lastName,     // Equivalent to lastName: lastName
    age          // Equivalent to age: age
};

/*
Object Property Shorthand:
- When property name matches variable name, you can omit the colon and value
- Reduces redundancy and improves readability
- Works with any variable type
- Can mix shorthand and traditional syntax in same object
*/

console.log(person.age);    // Access property → 30

// NESTED OBJECT EXAMPLE FOR DESTRUCTURING
const todo = {
    id: 1,
    title: 'Take out trash',
    user: {
        name: 'John'
    }
};

/*
Nested Object Structure:
- Objects can contain other objects as properties
- Creates hierarchical data structures
- Common pattern in APIs and complex data
- Access with chaining: todo.user.name
*/

// DESTRUCTURING EXAMPLES (Commented for demonstration)
/*
// Basic Object Destructuring
const { id, title } = todo;
// Extracts: id = 1, title = 'Take out trash'

// Destructuring with Renaming
const { id: todoId, title: todoTitle } = todo;
// Creates: todoId = 1, todoTitle = 'Take out trash'

// Nested Destructuring
const { user: { name } } = todo;
// Extracts: name = 'John'

// Destructuring with Default Values
const { id, title, completed = false } = todo;
// Creates: id = 1, title = 'Take out trash', completed = false

// Array Destructuring
const colors = ['red', 'green', 'blue'];
const [first, second, third] = colors;
// Creates: first = 'red', second = 'green', third = 'blue'
*/
*/

/*
=============================================================================
                            JSON (JavaScript Object Notation)
=============================================================================
JSON is a lightweight data interchange format that's easy for humans to read
and write, and easy for machines to parse and generate.
=============================================================================
*/

/*
// JSON CONVERSION EXAMPLES
const post = {
    id: 1,
    title: 'Post One',
    body: 'This is the body'
};

/*
JavaScript Object vs JSON:
- JavaScript Object: Native JS data structure with methods and functions
- JSON: String representation of data, language-independent
- JSON supports: strings, numbers, booleans, null, objects, arrays
- JSON does NOT support: functions, undefined, comments, dates (as objects)
*/

// CONVERT OBJECT TO JSON STRING
const str = JSON.stringify(post);
/*
JSON.stringify():
- Converts JavaScript object/array to JSON string
- Returns string representation
- Example result: '{"id":1,"title":"Post One","body":"This is the body"}'
- Optional parameters:
  - JSON.stringify(obj, replacer, space)
  - replacer: function or array to filter properties
  - space: number or string for indentation
*/

// CONVERT JSON STRING TO OBJECT
const obj = JSON.parse(str);
/*
JSON.parse():
- Converts JSON string back to JavaScript object
- Throws error if string is not valid JSON
- Returns native JavaScript object/array
- Optional reviver parameter for custom parsing
*/

console.log(str);    // Output: JSON string
console.log(obj);    // Output: JavaScript object

/*
Common JSON Use Cases:
1. API Communication: Send/receive data between client and server
2. Data Storage: Store configuration or data in files
3. Local Storage: Browser storage (localStorage, sessionStorage)
4. Data Exchange: Between different programming languages

JSON Rules:
- Property names must be in double quotes
- String values must be in double quotes
- No trailing commas allowed
- No comments allowed
- No undefined values (use null instead)

Example Valid JSON:
{
  "name": "John",
  "age": 30,
  "isActive": true,
  "address": null,
  "hobbies": ["reading", "coding"]
}
*/
*/


/*
=============================================================================
                    SECTION 2: FUNCTIONS, SCOPE & EXECUTION CONTEXT
=============================================================================
Functions are reusable blocks of code that perform specific tasks.
They are fundamental building blocks of JavaScript programming.
=============================================================================
*/

// *********** FUNCTIONS, SCOPE & EXECUTION CONTEXT ***********

/*
=============================================================================
                            FUNCTION BASICS
=============================================================================
Functions encapsulate code for reusability, organization, and modularity.
They can accept inputs (parameters) and return outputs.
=============================================================================
*/

/*
// BASIC FUNCTION DECLARATION
function sayHello() {
    console.log('Hello');
}
sayHello();    // Function call/invocation → Output: "Hello"

/*
Function Declaration Components:
1. 'function' keyword - declares a function
2. Function name (sayHello) - identifier to call the function
3. Parentheses () - contain parameters (empty in this case)
4. Curly braces {} - contain the function body
5. Function call - sayHello() executes the function
*/

// FUNCTION WITH PARAMETERS (NO RETURN)
function add(num1, num2) {
    console.log(num1 + num2);    // Performs action but doesn't return value
}
add(5, 10);    // Function call with arguments → Output: 15

/*
Parameters vs Arguments:
- Parameters: Variables in function definition (num1, num2)
- Arguments: Actual values passed when calling function (5, 10)
- This function performs an action (console.log) but doesn't return a value
*/

// FUNCTION WITH PARAMETERS AND RETURN VALUE
function subtract(num1, num2) {
    return num1 - num2;    // Returns calculated value
}
const result = subtract(10, 5);    // Store returned value
console.log(result);               // Output: 5

/*
Return Statement:
- 'return' keyword sends value back to caller
- Function execution stops at return statement
- Functions without return statement return 'undefined'
- Returned values can be stored in variables or used directly
*/

/*
Function Benefits:
1. Reusability: Write once, use multiple times
2. Organization: Break complex problems into smaller parts
3. Modularity: Separate concerns and responsibilities
4. Testing: Easier to test individual functions
5. Maintenance: Changes in one place affect all uses
*/
*/

/*
=============================================================================
                        ADVANCED PARAMETERS & ARGUMENTS
=============================================================================
Modern JavaScript provides flexible ways to handle function parameters
including default values, rest parameters, and complex data types.
=============================================================================
*/

/*
// DEFAULT PARAMETERS

// Function WITHOUT default parameter
function registerUser(user) {
    return user + ' is registered';
}
console.log(registerUser('John'));    // Output: "John is registered"
// console.log(registerUser());        // Would output: "undefined is registered"

// Function WITH default parameter (ES6)
function registerUser(user = 'Bot') {
    return user + ' is registered';
}
console.log(registerUser());          // Output: "Bot is registered"
console.log(registerUser('Alice'));   // Output: "Alice is registered"

/*
Default Parameters:
- Provide fallback values when arguments are not passed
- Use assignment operator (=) in parameter list
- Default values can be expressions or function calls
- Only used when argument is undefined (not null or false)
- Examples:
  - function greet(name = 'Guest') { ... }
  - function calculate(x, y = x * 2) { ... }
  - function timestamp(date = new Date()) { ... }
*/

// REST PARAMETERS (...args)
function sum(...numbers) {
    let total = 0;
    for (const num of numbers) {
        total += num;
    }
    return total;
}
console.log(sum(1, 2, 3, 4, 5));      // Output: 15
console.log(sum(10, 20));             // Output: 30
console.log(sum(1));                  // Output: 1

/*
Rest Parameters (...):
- Collects remaining arguments into an array
- Must be the last parameter
- Allows functions to accept unlimited arguments
- Creates a real array (not array-like object)
- Examples:
  - function log(first, ...rest) { ... }
  - function combine(separator, ...items) { ... }
*/

// OBJECTS AS PARAMETERS
function loginUser(user) {
    return `The user ${user.name} with the id of ${user.id} is logged in`;
}
const user = {
    id: 1,
    name: 'John'
};
console.log(loginUser(user));         // Output: "The user John with the id of 1 is logged in"

/*
Objects as Parameters:
- Pass complex data structures to functions
- Access properties using dot notation
- Objects are passed by reference (modifications affect original)
- Consider destructuring for cleaner code:
  function loginUser({ name, id }) {
    return `The user ${name} with the id of ${id} is logged in`;
  }
*/

// ARRAYS AS PARAMETERS
function getRandom(arr) {
    const randomIndex = Math.floor(Math.random() * arr.length);
    const item = arr[randomIndex];
    console.log(item);
}
getRandom([1, 2, 3, 4, 5]);          // Output: Random number from array

/*
Arrays as Parameters:
- Pass collections of data to functions
- Arrays are passed by reference
- Use array methods and properties (length, etc.)
- Math.random() generates number between 0 and 1
- Math.floor() rounds down to nearest integer
- Pattern: Math.floor(Math.random() * array.length) for random index
*/

/*
Parameter Best Practices:
1. Use descriptive parameter names
2. Provide default values for optional parameters
3. Use destructuring for object parameters
4. Validate parameters when necessary
5. Keep parameter lists short (consider objects for many parameters)
6. Document expected parameter types and formats
*/
*/

/*
=============================================================================
                            SCOPE & VARIABLE ACCESS
=============================================================================
Scope determines where variables can be accessed in your code.
Understanding scope is crucial for avoiding bugs and writing maintainable code.
=============================================================================
*/

/*
// GLOBAL SCOPE EXAMPLE
let x = 100;    // Global variable - accessible everywhere

function run() {
    console.log(x, 'In The Function');    // Can access global variable
}
run();    // Output: "100 In The Function"

/*
Global Scope:
- Variables declared outside any function or block
- Accessible from anywhere in the program
- Stored in global object (window in browsers, global in Node.js)
- Should be used sparingly to avoid naming conflicts
*/

function add(a, b) {
    let y = 10;    // Local variable - only accessible within this function
    console.log(y, 'In The Function Scope');    // Output: "10 In The Function Scope"
    console.log(x + y);                         // Can access both global (x) and local (y)
}
add();    // Output: "110"

/*
Function Scope:
- Variables declared inside a function
- Only accessible within that function
- Created when function is called, destroyed when function ends
- Parameters are also function-scoped
- Inner functions can access outer function variables (closure)
*/

/*
Scope Chain:
1. JavaScript looks for variables in current scope first
2. If not found, looks in outer scope
3. Continues up the chain until global scope
4. If variable not found anywhere, ReferenceError is thrown

Example Scope Chain:
Global Scope (x = 100)
  └── Function Scope (y = 10, can access x)
      └── Block Scope (can access x and y)
*/
*/

/*
=============================================================================
                            BLOCK SCOPE (ES6)
=============================================================================
Block scope was introduced in ES6 with let and const declarations.
It provides more precise control over variable accessibility.
=============================================================================
*/

/*
// BLOCK SCOPE WITH CONST AND LET
const x = 100;    // Global scope

if (true) {
    const y = 200;        // Block scope - only accessible within this block
    console.log(x + y);   // Can access global x and local y → Output: 300
}
// console.log(y);       // ReferenceError: y is not defined

/*
Block Scope Rules:
- Created by curly braces {} (if, for, while, try/catch, etc.)
- let and const are block-scoped
- Variables only accessible within the block where they're declared
- Provides better encapsulation and prevents variable leakage
*/

// LOOP BLOCK SCOPE
for (let i = 0; i <= 10; i++) {
    console.log(i);    // i is accessible within loop block
}
// console.log(i);    // ReferenceError: i is not defined

/*
Loop Block Scope:
- Loop variables (let i) are scoped to the loop block
- Each iteration gets its own copy of the variable
- Prevents common closure issues in loops
- var would create function-scoped variable instead
*/

// COMPARING VAR, LET, AND CONST SCOPE
if (true) {
    const a = 500;    // Block-scoped
    let b = 600;      // Block-scoped
    var c = 700;      // Function-scoped (or global if not in function)
}
// console.log(a);   // ReferenceError: a is not defined
// console.log(b);   // ReferenceError: b is not defined
console.log(c);      // Output: 700 (var is accessible outside block!)

/*
Variable Declaration Comparison:

var:
- Function-scoped or globally-scoped
- Hoisted (can be used before declaration)
- Can be redeclared
- Ignores block scope

let:
- Block-scoped
- Hoisted but in "temporal dead zone"
- Cannot be redeclared in same scope
- Respects block scope

const:
- Block-scoped
- Hoisted but in "temporal dead zone"
- Cannot be redeclared or reassigned
- Must be initialized at declaration
- Respects block scope

Best Practice: Use const by default, let when reassignment needed, avoid var
*/
*/

/*
=============================================================================
                    FUNCTION DECLARATION VS EXPRESSION
=============================================================================
JavaScript provides multiple ways to create functions, each with different
characteristics regarding hoisting, naming, and usage patterns.
=============================================================================
*/

/*
// FUNCTION DECLARATION
function addDollarSign(value) {
    return '$' + value;
}
console.log(addDollarSign(100));    // Output: "$100"

/*
Function Declaration Characteristics:
- Uses 'function' keyword followed by name
- Hoisted completely (can be called before declaration)
- Creates named function
- Function name is available in containing scope
- Can be called anywhere in the scope

Example of hoisting:
sayHello(); // This works!
function sayHello() {
    console.log("Hello!");
}
*/

// FUNCTION EXPRESSION
const addPlusSign = function(value) {
    return '+' + value;
};
console.log(addPlusSign(100));      // Output: "+100"

/*
Function Expression Characteristics:
- Function assigned to a variable
- Not hoisted (cannot be called before assignment)
- Can be anonymous or named
- Variable name used to call function
- More explicit about when function is available

Example of no hoisting:
// sayGoodbye(); // ReferenceError: Cannot access before initialization
const sayGoodbye = function() {
    console.log("Goodbye!");
};
*/

/*
When to Use Each:

Function Declarations:
- When you need hoisting behavior
- For main/primary functions in a module
- When function needs to be available throughout scope
- Traditional approach, widely understood

Function Expressions:
- When you want explicit control over when function is available
- For conditional function creation
- When passing functions as arguments
- When assigning functions to object properties
- More modern approach, prevents hoisting issues

Named Function Expressions:
const factorial = function fact(n) {
    return n <= 1 ? 1 : n * fact(n - 1);
};
// 'factorial' is available outside, 'fact' only inside function
*/
*/

/*
=============================================================================
                            ARROW FUNCTIONS (ES6)
=============================================================================
Arrow functions provide a more concise syntax for writing functions
and have different behavior regarding 'this' binding.
=============================================================================
*/

/*
// BASIC ARROW FUNCTION SYNTAX
const addSign = (value) => {
    return '$' + value;
};
console.log(addSign(100));          // Output: "$100"

/*
Arrow Function Syntax Variations:

1. Basic syntax with parentheses and braces:
   const func = (param) => { return param * 2; }

2. Single parameter (parentheses optional):
   const func = param => { return param * 2; }

3. Multiple parameters (parentheses required):
   const func = (a, b) => { return a + b; }

4. No parameters (parentheses required):
   const func = () => { return "Hello"; }

5. Single expression (implicit return):
   const func = (a, b) => a + b;

6. Returning object literal (wrap in parentheses):
   const func = (name) => ({ name: name, age: 30 });
*/

// MULTIPLE PARAMETER EXAMPLES
const add = (a, b) => {
    return a + b;
};
console.log(add(100, 200));         // Output: 300

const subtract = (a, b) => {
    return a - b;
};
console.log(subtract(200, 100));    // Output: 100

// CONCISE ARROW FUNCTION EXAMPLES (Implicit Return)
/*
const multiply = (a, b) => a * b;                    // Single expression
const square = x => x * x;                           // Single parameter
const greet = () => "Hello World!";                  // No parameters
const createUser = name => ({ name, active: true }); // Return object

console.log(multiply(5, 3));        // Output: 15
console.log(square(4));             // Output: 16
console.log(greet());               // Output: "Hello World!"
console.log(createUser("John"));    // Output: { name: "John", active: true }
*/

/*
Arrow Functions vs Regular Functions:

1. Syntax: More concise, especially for simple functions
2. 'this' binding: Arrow functions inherit 'this' from enclosing scope
3. Hoisting: Not hoisted (like function expressions)
4. Arguments object: No 'arguments' object available
5. Constructor: Cannot be used as constructors (no 'new')
6. Method definitions: Not ideal for object methods

When to Use Arrow Functions:
- Short, simple functions
- Callback functions (map, filter, reduce)
- When you need lexical 'this' binding
- Functional programming patterns

When NOT to Use Arrow Functions:
- Object methods (when you need 'this' to refer to object)
- Event handlers (when you need 'this' to refer to element)
- Functions that need 'arguments' object
- Constructor functions
*/
*/

/*
=============================================================================
                IMMEDIATELY INVOKED FUNCTION EXPRESSION (IIFE)
=============================================================================
IIFE is a function that runs immediately after it's defined.
It's used to create private scope and avoid polluting the global namespace.
=============================================================================
*/

/*
// BASIC IIFE SYNTAX
(function() {
    console.log('IIFE executed immediately!');
})();    // Output: "IIFE executed immediately!"

/*
IIFE Syntax Breakdown:
1. (function() { ... }) - Function expression wrapped in parentheses
2. () - Immediately invoke the function
3. The outer parentheses make it a function expression (not declaration)

Alternative Syntax:
(function() {
    console.log('Alternative IIFE syntax');
}());

Both are valid, first is more common.
*/

// IIFE WITH PARAMETERS
/*
(function(name, age) {
    console.log(`Hello ${name}, you are ${age} years old`);
})('John', 30);    // Output: "Hello John, you are 30 years old"
*/

// IIFE WITH RETURN VALUE
/*
const result = (function(a, b) {
    return a + b;
})(5, 3);
console.log(result);    // Output: 8
*/

// ARROW FUNCTION IIFE
/*
(() => {
    console.log('Arrow function IIFE');
})();
*/

/*
IIFE Use Cases:

1. Module Pattern (Before ES6 modules):
const myModule = (function() {
    let privateVar = 'I am private';

    return {
        publicMethod: function() {
            return privateVar;
        }
    };
})();

2. Avoiding Global Pollution:
(function() {
    // All variables here are private
    let tempData = processData();
    // Use tempData without polluting global scope
})();

3. One-time Initialization:
(function() {
    // Setup code that runs once
    initializeApp();
    setupEventListeners();
})();

4. Creating Closures:
const counter = (function() {
    let count = 0;
    return function() {
        return ++count;
    };
})();

Benefits:
- Creates private scope
- Prevents variable name conflicts
- Executes immediately
- Self-contained code blocks
- Memory efficient (variables can be garbage collected)

Modern Alternatives:
- ES6 modules (import/export)
- Block scope with let/const
- Classes for encapsulation
*/
*/


/*
=============================================================================
                    SECTION 3: LOGIC & CONTROL FLOW
=============================================================================
Control flow statements determine the order in which code executes.
Loops allow repetitive execution, conditions control branching.
=============================================================================
*/

// *********** LOGIC & CONTROL FLOW ***********

/*
=============================================================================
                            FOR LOOPS
=============================================================================
For loops are used when you know how many times you want to repeat code.
They provide initialization, condition, and increment in one statement.
=============================================================================
*/

/*
// BASIC FOR LOOP EXAMPLES

// Loop from 0 to 10 (inclusive)
for (let i = 0; i <= 10; i++) {
    console.log(i);    // Output: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10
}

/*
For Loop Syntax:
for (initialization; condition; increment) {
    // code to execute
}

Components:
1. Initialization: let i = 0 (runs once at start)
2. Condition: i <= 10 (checked before each iteration)
3. Increment: i++ (runs after each iteration)
4. Loop body: code inside braces
*/

// Loop from 0 to 9 (exclusive of 10)
for (let i = 0; i < 10; i++) {
    console.log(i);    // Output: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9
}

// FOR LOOP WITH CONDITIONAL LOGIC
for (let i = 0; i <= 10; i++) {
    if (i === 5) {
        console.log('5 is my favorite number');
        continue;    // Skip rest of current iteration, go to next
    }
    console.log(i);
}
/*
Output:
0, 1, 2, 3, 4, "5 is my favorite number", 6, 7, 8, 9, 10

Control Flow Keywords:
- continue: Skip current iteration, go to next
- break: Exit loop completely
- return: Exit function (and loop if inside function)
*/

// NESTED FOR LOOPS (Multiplication Table)
for (let i = 1; i <= 10; i++) {
    console.log('Number ' + i);
    for (let j = 1; j <= 10; j++) {
        console.log(`${i} * ${j} = ${i * j}`);
    }
}

/*
Nested Loops:
- Inner loop completes fully for each iteration of outer loop
- Useful for 2D data structures, matrices, tables
- Be careful with performance - complexity is O(n²)
- Example creates multiplication table from 1x1 to 10x10

Loop Variations:
- for (let i = 10; i >= 0; i--) // Count backwards
- for (let i = 0; i < 100; i += 5) // Increment by 5
- for (let i = 1; i < 1000; i *= 2) // Multiply by 2 each time
*/
*/

/*
//While & Do While Loops

/!*let i = 0;
while (i <= 10){
    console.log('Number ' + i);
    i++;
}*!/

//loop through an array
/!*const arr = [1, 2, 3, 4, 5];
let j = 0;
while (j < arr.length){
    console.log(arr[j]);
    j++;
}*!/

//Nested While Loops
/!*let k = 0;
while (k <= 10){
    let l = 0;
    while (l <= 10){
        console.log(`${k} * ${l} = ${k * l}`);
        l++;
    }
    k++;
}*!/
/!*let i = 1;
do {
    console.log('Number ' + i);
    i++;
} while (i <= 10);*!/
*/

/*//FizBuzz Challenge
for (let i = 1; i <= 100; i++) {
    if (i % 15 === 0) {
        console.log('FizzBuzz');
    } else if (i % 3 === 0) {
        console.log('Fizz');
    } else if (i % 5 === 0) {
        console.log('Buzz');
    } else {
        console.log(i);
    }
}*/

/*//Array.forEach()
const socials = ['Twitter', 'LinkedIn', 'Facebook', 'Instagram'];
/!*socials.forEach(function (item) {
    console.log(item);})*!/
socials.forEach((item) => console.log(item));
socials.forEach((item, index, array) => console.log(`${index} - ${item}`));*/

/*
// Array.filter
/!*const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const evenNumbers = numbers.filter((number) => number % 2 === 0);
const oddNumbers = numbers.filter((number) => number % 2 !== 0);
console.log(evenNumbers);
console.log(oddNumbers);*!/

const companies = [
    {name: 'Company One', category: 'Finance', start: 1981, end: 2004},
    {name: 'Company Two', category: 'Retail', start: 1992, end: 2008},
    {name: 'Company Three', category: 'Auto', start: 1999, end: 2007},
    {name: 'Company Four', category: 'Retail', start: 1989, end: 2010},
    {name: 'Company Five', category: 'Technology', start: 2009, end: 2014},
    {name: 'Company Six', category: 'Finance', start: 1987, end: 2010},
    {name: 'Company Seven', category: 'Auto', start: 1986, end: 1996},
    {name: 'Company Eight', category: 'Technology', start: 2011, end: 2016},
    {name: 'Company Nine', category: 'Retail', start: 1981, end: 1989}
];

const retailCompanies = companies.filter((company) => company.category === 'Retail');
console.log(retailCompanies);

const eightiesCompanies = companies.filter((company) => company.start >= 1980 && company.end <= 2005);
console.log(eightiesCompanies);*/

/*//Array.map()
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const squareNumbers = numbers.map((number) => number * number);
console.log(squareNumbers);

const companies = [
    {name: 'Company One', category: 'Finance', start: 1981, end: 2004},
    {name: 'Company Two', category: 'Retail', start: 1992, end: 2008},
    {name: 'Company Three', category: 'Auto', start: 1999, end: 2007},
    {name: 'Company Four', category: 'Retail', start: 1989, end: 2010},
    {name: 'Company Five', category: 'Technology', start: 2009, end: 2014},
    {name: 'Company Six', category: 'Finance', start: 1987, end: 2010},
    {name: 'Company Seven', category: 'Auto', start: 1986, end: 1996},
    {name: 'Company Eight', category: 'Technology', start: 2011, end: 2016},
    {name: 'Company Nine', category: 'Retail', start: 1981, end: 1989}
];

//Array with company Names
const companyNames = companies.map((company) => company.name);
console.log(companyNames);

const companyInfo = companies.map((company) => {
    return {
        name: company.name,
        category: company.category,
    };
});
console.log(companyInfo);*/

/*
//Array.reduce()
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const sum = numbers.reduce((acc, cur) => acc + cur, 0);
console.log(sum);

const cart = [
    {id: 1, name: 'Product 1', price: 100},
    {id: 2, name: 'Product 2', price: 200},
    {id: 3, name: 'Product 3', price: 300},
];
const total = cart.reduce((acc, cur) => acc + cur.price, 0);
console.log(total);*/

/*
//Array Challenge
const people = [
    {
        name: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        age: 30,
        phone: '************',
    },
    {
        name: 'Jane',
        lastName: 'Poe',
        email: '<EMAIL>',
        age: 25,
        phone: '************',
    },
    {
        name: 'Bob',
        lastName: 'Roe',
        email: '<EMAIL>',
        age: 28,
        phone: '************',
    },
    {
        name: 'Sam',
        lastName: 'Sulek',
        email: '<EMAIL>',
        age: 32,
        phone: '************',
    },
]

const youngPeople = people.filter((person) => person.age < 30);
const youngPeopleNames = youngPeople.map((person) => person.name);
const youngPeopleNamesAndAges = youngPeople.map((person) => {
    return {
        name: person.name,
        age: person.age,
    };
});
const youngPeopleNamesAndAgesAndEmail = youngPeople.map((person) => {
    return {
        name: person.name,
        age: person.age,
        email: person.email,
    };
});

console.log(youngPeopleNamesAndAgesAndEmail);*/
