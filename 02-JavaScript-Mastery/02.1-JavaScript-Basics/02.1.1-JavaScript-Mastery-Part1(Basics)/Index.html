<!doctype html>
<!--
    HTML5 Document Type Declaration
    - Tells the browser this is an HTML5 document
    - Must be the first line in any HTML document
    - Ensures the browser renders the page in standards mode
-->
<html lang="en">
<!--
    Root HTML element with language attribute
    - lang="en" specifies the document language as English
    - Helps screen readers and search engines understand the content
    - Improves accessibility and SEO
-->
<head>
    <!--
        Document metadata section
        - Contains information about the document that isn't displayed
        - Includes character encoding, viewport settings, title, etc.
    -->

    <!-- Character encoding declaration -->
    <meta charset="UTF-8">
    <!--
        UTF-8 encoding supports all Unicode characters
        - Allows display of international characters
        - Should be declared early in the <head> section
        - Prevents character encoding issues
    -->

    <!-- Viewport meta tag for responsive design -->
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <!--
        Viewport configuration for mobile devices:
        - width=device-width: Sets viewport width to device width
        - user-scalable=no: Prevents user from zooming in/out
        - initial-scale=1.0: Sets initial zoom level to 100%
        - maximum-scale=1.0: Prevents zooming beyond 100%
        - minimum-scale=1.0: Prevents zooming below 100%
    -->

    <!-- Internet Explorer compatibility -->
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <!--
        Forces Internet Explorer to use the latest rendering engine
        - Ensures consistent behavior across IE versions
        - "edge" mode uses the highest available document mode
    -->

    <!-- Document title -->
    <title>JavaScript Mastery Part 1 - Basics & Fundamentals</title>
    <!--
        Page title displayed in browser tab
        - Important for SEO and bookmarking
        - Should be descriptive and unique
    -->
</head>
<body>
    <!--
        Document body - contains all visible content
        - Everything users see and interact with goes here
    -->

    <!-- Main heading -->
    <h1>JavaScript Mastery Part 1: Basics & Fundamentals</h1>
    <!--
        Primary heading of the page
        - h1 should be used once per page for the main title
        - Important for SEO and document structure
        - Screen readers use headings for navigation
    -->

    <!-- External JavaScript file inclusion -->
    <script src="Script.js"></script>
    <!--
        Links to external JavaScript file
        - src attribute specifies the file path
        - Placed at end of body for better page load performance
        - JavaScript will execute after HTML content is loaded
        - Contains all the JavaScript examples and demonstrations
    -->
</body>
</html>